from abc import ABC, abstractmethod
import asyncio
from time import time
from typing import Any, Dict, Optional, Type, Union
from pydantic import ValidationError
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.logging.logger_config import get_module_logger
from schemas.layer2_schema import (
    STTInputSchema, STTOutputSchema,
    PreProcessingInputSchema, PreProcessingOutputSchema,
    ProcessingInputSchema, ProcessingOutputSchema,
    FillerInputSchema, FillerOutputSchema,
    TTSInputSchema, TTSOutputSchema
)
from core.memory.tts_cache import TTSCache
from utils.audio_utils import record_microphone_audio_vad

"""
Pipeline States v2
------------------
This module defines the abstract base class and concrete pipeline state classes for the new state-based pipeline management system.
Each state retrieves its agent from the agent registry, uses the agent's process method, and handles messaging, validation, and error handling.

Enhanced with proper Pydantic schema validation for input/output data.
"""

# --- Input/Output Schema Definitions are now in schemas/pipelineStateV2InputOutput.py ---

class AbstractPipelineState(ABC):
    """
    Abstract base class for all pipeline states.

    Provides schema validation, agent orchestration, and notification publishing.
    Each concrete state must define input_schema_class and output_schema_class.
    """
    id: str
    input_schema_class: Type = None
    output_schema_class: Type = None

    def __init__(self, state_id: str, agent_registry: Any, session_id: str):
        self.id = state_id
        self.agent_registry = agent_registry
        self.session_id = session_id
        self.logger = get_module_logger("pipeline_state", session_id=self.session_id, state_id=self.id)

    @abstractmethod
    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        pass

    # @classmethod
    # def get_input_schema(cls) -> Dict[str, Any]:
    #     """Get input schema as dictionary for compatibility"""
    #     if cls.input_schema_class:
    #         return cls.input_schema_class.model_json_schema()
    #     return {}

    # @classmethod
    # def get_output_schema(cls) -> Dict[str, Any]:
    #     """Get output schema as dictionary for compatibility"""
    #     if cls.output_schema_class:
    #         return cls.output_schema_class.model_json_schema()
    #     return {}

    def validate_input(self, input_data: Dict[str, Any]) -> Any:
        if not self.input_schema_class:
            self.logger.warning(f"No input schema defined for state {self.id}")
            return input_data
        try:
            validated_input = self.input_schema_class(**input_data)
            self.logger.debug(f"Input validation successful for state {self.id}")
            return validated_input
        except ValidationError as e:
            self.logger.error(f"Input validation failed for state {self.id}: {e}")
            raise

    def validate_output(self, output_data: Dict[str, Any]) -> Any:
        if not self.output_schema_class:
            self.logger.warning(f"No output schema defined for state {self.id}")
            return output_data
        try:
            validated_output = self.output_schema_class(**output_data)
            self.logger.debug(f"Output validation successful for state {self.id}")
            return validated_output
        except ValidationError as e:
            self.logger.error(f"Output validation failed for state {self.id}: {e}")
            raise

    async def _publish_notification(self, status: str, payload: Dict[str, Any], context_keys_updated=None, error_message=None):
        notification = A2AMessage(
            session_id=self.session_id,
            message_type=MessageType.NOTIFICATION,
            source_agent=self.id,
            target_agent="Orchestrator",
            payload={"status": status, **({"error_message": error_message} if error_message else {}), **payload},
            context_keys_updated=context_keys_updated or []
        )
        redis_client = self.agent_registry._redis
        await redis_client.publish("agent_completion", notification.to_json())


# --- Concrete Pipeline States ---

class STTState(AbstractPipelineState):
    """
    Speech-to-Text State
    Converts audio input to text transcript with latency tracking.
    """
    input_schema_class = STTInputSchema
    output_schema_class = STTOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        # Handle input acquisition - check for existing audio or capture from microphone
        processed_input_data = await self._handle_audio_input(input_data, context)

        # Check if _handle_audio_input returned a StateOutput (e.g., no speech detected)
        if isinstance(processed_input_data, StateOutput):
            return processed_input_data
        try:
            validated_input = self.validate_input(processed_input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("stt_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"STTState output validation failed: {e}")
            await self._publish_notification("complete", {"latencySTT": result.outputs.get("latencySTT")}, ["transcript", "latencySTT"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )
    
    async def _handle_audio_input(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], StateOutput]:
        """Handle audio input acquisition - always capture from microphone for user input."""

        # For turn-based conversation, STT should ALWAYS capture from microphone
        # Ignore any TTS audio paths that might be in memory from AI responses

        print("🎤 [STT] Capturing user input from microphone...")

        # Get memory manager from context
        memory_manager = context.get("memory_manager") if context else None

        # Get microphone device index from memory (set by test)
        device_index = None
        if memory_manager:
            device_index = await memory_manager.get("microphone_device_index")

        # Record audio from microphone using VAD with extended timeout for natural conversation
        from utils.audio_utils import record_microphone_audio_vad

        # Use longer timeout for turn-based conversation (15 seconds max recording, 3 seconds silence)
        user_audio_path = await record_microphone_audio_vad(
            device_index=device_index,
            silence_duration=3.0,  # Allow 3 seconds of silence before stopping
            max_recording=15.0     # Maximum 15 seconds of recording
        )

        if user_audio_path is None:
            # In turn-based conversation, no speech after reasonable time means user might be done
            # Don't immediately play fallback - let the orchestrator handle this gracefully
            print("🔇 [STT] No speech detected - user may be taking time to think or may be done speaking")

            # Return a special status indicating "waiting for user input" rather than error
            return StateOutput(
                status=StatusType.SUCCESS,  # Not an error - just waiting
                message="No speech detected - waiting for user input",
                code=StatusCode.NO_CONTENT,
                outputs={
                    "transcript": "",
                    "waiting_for_input": True,
                    "no_speech_detected": True
                },
                meta={
                    "agent": self.id,
                    "status": "waiting_for_user_input"
                }
            )

        # Save the recorded audio path to memory for potential reuse
        if memory_manager:
            await memory_manager.set("contextual", "user_input_audio_path", user_audio_path)

        print(f"✅ [STT] User audio captured from microphone: {user_audio_path}")
        return {"audio_path": user_audio_path}






class PreProcessingState(AbstractPipelineState):
    """
    Preprocessing State
    Cleans text and extracts intent, emotion, and gender information.
    """
    input_schema_class = PreProcessingInputSchema
    output_schema_class = PreProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("preprocessing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"PreProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyPreprocessing": result.outputs.get("latencyPreprocessing")}, ["clean_text", "intent", "emotion", "gender", "latencyPreprocessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class ProcessingState(AbstractPipelineState):
    """
    Processing State
    Processes clean text and intent to generate LLM responses and business logic results.
    """
    input_schema_class = ProcessingInputSchema
    output_schema_class = ProcessingOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("processing_agent")
        try:
            result = await agent.process(validated_input.model_dump(), context)
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"ProcessingState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyProcessing": result.outputs.get("latencyProcessing")}, ["llm_answer", "account_balance", "loan_eligibility", "exit_signal", "latencyProcessing"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class FillerState(AbstractPipelineState):
    """
    Filler State
    Generates filler audio content for conversation flow management.
    """
    input_schema_class = FillerInputSchema
    output_schema_class = FillerOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("filler_tts_agent")
        try:
            startTime = asyncio.get_event_loop().time()
            key = ""
            if context["state_id"] and context["persona"] and "filler_text" in input_data:
                key = await TTSCache.generateKey(
                    state=context["state_id"],
                    text=input_data["filler_text"],
                    persona=context["persona"]
                )
                context["key"] = key
            TTSAudioPath = await TTSCache.get(key)
            expiryPeriod = await TTSCache.getExpiryPeriod(key)
            result = None
            if expiryPeriod > 100 and TTSAudioPath:
                self.logger.info(f"Using cached filler TTS audio for key: {key}")
                result = StateOutput(
                    status=StatusType.SUCCESS,
                    message="Filler TTS cache hit",
                    code=StatusCode.OK,
                    outputs={"audio_path": TTSAudioPath, "filler_text": input_data["filler_text"], "latencyFiller": int((asyncio.get_event_loop().time() - startTime) * 1000)},
                    meta={"agent": self.id, "cache_hit": True}
                )
            else:
                result = await agent.process(validated_input.model_dump(), context)
                if result.outputs.get("audio_path"):
                    if key != "":
                        expires_in = input_data.get("expires_in")
                        if expires_in is None:
                            await TTSCache.set(key, result.outputs["audio_path"])
                        else:
                            await TTSCache.set(key, result.outputs["audio_path"], expires_in=input_data["expires_in"])
                        self.logger.info(f"Cached filler TTS audio for key: {key}")
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"FillerState output validation failed: {e}")
            await self._publish_notification("filler", {"audio_path": result.outputs.get("audio_path"), "filler_text": result.outputs.get("filler_text")}, ["audio_path", "filler_text"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"FillerState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

class TTSState(AbstractPipelineState):
    """
    Text-to-Speech State
    Converts text to speech with emotion and gender parameters.
    """
    input_schema_class = TTSInputSchema
    output_schema_class = TTSOutputSchema

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        try:
            validated_input = self.validate_input(input_data)
        except ValidationError as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )
        agent = self.agent_registry.getAgent("tts_agent")
        try:
            startTime = asyncio.get_event_loop().time()
            key = ""
            if context["state_id"] and context["persona"]:
                key = await TTSCache.generateKey(
                    state=context["state_id"],
                    text=input_data["text"],
                    persona=context["persona"]
                )
                context["key"] = key           
            TTSAudioPath = await TTSCache.get(key)
            expiryPeriod = await TTSCache.getExpiryPeriod(key)
            result = None
            if expiryPeriod > 100 and TTSAudioPath:
                self.logger.info(f"Using cached TTS audio for key: {key}")
                result = StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS cache hit",
                    code=StatusCode.OK,
                    outputs={"audio_path": TTSAudioPath, "latencyTTS": int((asyncio.get_event_loop().time() - startTime) * 1000)},
                    meta={"agent": self.id, "cache_hit": True}
                )
            else:
                result = await agent.process(validated_input.model_dump(), context)
                if result.outputs.get("audio_path"):
                    if key != "":
                        expires_in = input_data.get("expires_in")
                        if expires_in is None:
                            await TTSCache.set(key, result.outputs["audio_path"])
                        else:    
                            await TTSCache.set(key, result.outputs["audio_path"], expires_in=input_data["expires_in"])
                        self.logger.info(f"Cached TTS audio for key: {key}")
            try:
                validated_output = self.validate_output(result.outputs)
                result.outputs = validated_output.model_dump()
            except ValidationError as e:
                self.logger.warning(f"TTSState output validation failed: {e}")
            await self._publish_notification("complete", {"latencyTTS": result.outputs.get("latencyTTS")}, ["audio_path", "latencyTTS"])
            return result
        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )
        
class RepeatedUserQueryState(AbstractPipelineState):
    """
    RepeatedUserQuery State

    Handles repeated user queries in looping workflows by:
    1. Sending engagement message EVERY TIME user reaches this state (repetitive by design)
    2. Resetting previous intent context for fresh parsing
    3. Capturing fresh user audio input via microphone
    4. Processing through STT for new transcript

    This enables conversation looping where users can make multiple requests
    in the same session without interference from previous intents.
    The engagement message is sent repeatedly for persistent user engagement.
    """
    input_schema_class = None  # No specific input schema needed
    output_schema_class = None  # No specific output schema needed

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        """
        Process repeated user query - this state is called BEFORE the Layer2 pipeline.

        Behavior: Sends engagement message EVERY TIME user reaches this state.
        No session-scoped check - asks the same question repeatedly for persistent engagement.

        It resets context and manages engagement, then the workflow continues to the Layer2 pipeline.

        Args:
            input_data: Input data (typically empty)
            context: Session context including memory_manager

        Returns:
            StateOutput: Engagement message (every time) or simple success response after context reset
        """
        try:
            # Get memory manager - StateManager doesn't pass it in context, so we need to get it from agent_registry
            memory_manager = None

            # Try to get memory manager from context first (for direct testing)
            if context:
                memory_manager = context.get("memory") or context.get("memory_manager")

            # If not in context, get it from the StateManager via agent_registry
            if not memory_manager and self.agent_registry:
                # The agent_registry should have access to the StateManager's memory_manager
                # For now, we'll create a new MemoryManager with the session_id
                from core.memory.memory_manager import MemoryManager
                session_id = context.get("session_id") if context else self.session_id
                memory_manager = MemoryManager(session_id)

            if not memory_manager:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Memory manager not available",
                    code=StatusCode.INTERNAL_ERROR,
                    outputs={},
                    meta={"agent": self.id}
                )

            # Step 1: Send engagement question (always for RepeatedUserQuery)
            engagement_response = await self._send_user_engagement(memory_manager)
            if engagement_response:
                return engagement_response

            # Step 2: Reset previous intent context for fresh parsing
            await self._reset_context(context)

            # Step 3: Return simple success - StateManager will continue to next pipeline step
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Ready for repeated user query",
                code=StatusCode.OK,
                outputs={},
                meta={
                    "agent": self.id,
                    "context_reset": True
                }
            )

        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"RepeatedUserQuery error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

    async def _reset_context(self, context: Optional[Dict[str, Any]] = None):
        """
        Reset previous intent context for fresh parsing.

        Clears intent-related memory keys to prevent interference from
        previous user requests in the same session.
        """
        # Get memory manager from context
        memory_manager = context.get("memory_manager") if context else None
        if not memory_manager:
            self.logger.warning("No memory manager available for context reset")
            return

        # Keys to clear for fresh intent parsing
        intent_keys = [
            "intent", "clean_text", "emotion", "gender",
            "intent_confidence", "preprocessing_confidence_value",
            "last_user_message", "last_user_intent", "transcript"
        ]

        # Clear intent-related keys
        for key in intent_keys:
            await memory_manager.delete(key)

        # Clear session-scoped intent keys (pattern: session_id_state_key)
        try:
            all_keys = await memory_manager.contextual.get_all_keys()
            session_id = context.get("session_id", "") if context else ""

            for key in all_keys:
                # Clear keys that contain intent-related data for this session
                if session_id and any(intent_key in key for intent_key in intent_keys):
                    if key.startswith(session_id):
                        await memory_manager.delete(key)
        except Exception as e:
            self.logger.warning(f"Error clearing session-scoped keys: {e}")

        # Set reset metadata
        from datetime import datetime
        await memory_manager.set("contextual", "context_reset_timestamp", datetime.now().isoformat())
        await memory_manager.set("contextual", "previous_context_cleared", True)

        self.logger.info("Reset intent context for fresh parsing",
                        action="_reset_context",
                        layer="repeated_user_query")



    async def _send_user_engagement(self, memory_manager) -> Optional[StateOutput]:
        """
        Send engagement question EVERY TIME user reaches RepeatedUserQueryState.

        No session-scoped check - asks engagement message repeatedly for persistent engagement.
        Stores the engagement message in memory for the Layer2 pipeline to pick up.
        """
        # Send engagement message immediately every time (no session check)
        engagement_message = "Is there anything else I can help you with today?"

        # Store engagement message in memory for Layer2 pipeline
        await memory_manager.set("contextual", "text", engagement_message)
        await memory_manager.set("contextual", "engagement_message", engagement_message)
        await memory_manager.set("contextual", "is_engagement_question", True)

        self.logger.info(
            "Engagement question generated and stored in memory for TTS",
            action="_send_user_engagement",
            layer="repeated_user_query"
        )

        return StateOutput(
            status=StatusType.SUCCESS,
            message="User engagement question",
            code=StatusCode.OK,
            outputs={
                "text": engagement_message,
                "is_engagement_question": True,
                "requires_yes_no_response": True
            },
            meta={
                "agent": self.id,
                "engagement_question": True,
                "awaiting_user_decision": True
            }
        )


{"id": "l2_repeated_user_query_simple", "version": "2.0", "pipeline": [{"step": "repeated_user_query", "process": "repeated_user_query_process", "agent": "repeated_user_query_state", "input": {}, "tools": {}, "output": {"text": "text", "engagement_message": "engagement_message", "is_engagement_question": "is_engagement_question"}}, {"step": "engagement_tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "text", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "engagement_audio_path", "latencyTTS": "engagement_latencyTTS"}}, {"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"emotion": "emotion", "intent": "intent", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"clean_text": "clean_text", "intent": "intent"}, "tools": {"external_tools": "openai"}, "output": {"llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "response_tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}
"""
Interrupt Integration Module

Integration layer that connects the InterruptHandler
with StateManager and provides the main interrupt handling interface.

This replaces the complex InterruptManager with a streamlined approach.
"""

from typing import Dict, Any, Optional

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.interruption.interrupt_handler import <PERSON>ruptHandler

from core.config.interrupt_config import get_interrupt_config


class InterruptIntegration:
    """
    Simple integration layer for interrupt handling.
    
    Provides a clean interface between StateManager and the interrupt system.
    Focuses on the essential 7-step flow without unnecessary complexity.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config or get_interrupt_config()
        self.logger = get_module_logger("InterruptIntegration", session_id=session_id)
        
        # Initialize the interrupt handler
        self.interrupt_handler = InterruptHandler(
            session_id=session_id,
            memory_manager=memory_manager,
            interrupt_config=self.interrupt_config
        )
        


    async def handle_tts_with_interrupts(self, tts_result: StateOutput, workflow_context: Dict[str, Any]) -> StateOutput:
        """
        Main entry point for TTS with interrupt support.
        
        Args:
            tts_result: Result from TTS agent containing audio_path
            workflow_context: Current workflow context for reversibility checking
            
        Returns:
            StateOutput with interrupt handling results
        """
        try:
            # Check if interrupts are enabled
            if not self.interrupt_config.global_settings.enabled:
                self.logger.info("Interrupts disabled, playing TTS normally")
                return tts_result
            
            # Extract audio path from TTS result
            audio_path = tts_result.outputs.get("audio_path")
            if not audio_path:
                self.logger.warning("No audio path in TTS result, cannot handle interrupts")
                return tts_result
            
            self.logger.info(
                "Starting TTS with interrupt support",
                action="handle_tts_with_interrupts",
                input_data={"audio_path": audio_path},
                layer="interrupt_integration"
            )
            
            # Use the simple interrupt handler for the 7-step flow
            interrupt_result = await self.interrupt_handler.handle_tts_with_interrupt_support(
                audio_path=audio_path,
                workflow_context=workflow_context
            )
            
            # Process any queued user input if applicable
            if interrupt_result.outputs.get("user_input_queued"):
                await self._handle_queued_user_input(
                    interrupt_result.outputs.get("user_interrupt_input")
                )
            
            return interrupt_result

        except Exception as e:
            self.logger.error(
                "Error in TTS interrupt handling",
                action="handle_tts_with_interrupts",
                reason=str(e),
                layer="interrupt_integration"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt handling error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def handle_tts_with_real_concurrent_monitoring(self, tts_result: StateOutput, workflow_context: Dict[str, Any] = None) -> StateOutput:
        """
        Handle TTS with REAL concurrent monitoring - actual audio playback with microphone monitoring.

        This method provides true concurrent functionality:
        1. Plays actual TTS audio
        2. Simultaneously monitors microphone for interrupts
        3. Pauses TTS immediately when interrupt detected

        Args:
            tts_result: Result from TTS agent containing audio_path
            workflow_context: Current workflow context for reversibility checking

        Returns:
            StateOutput with real interrupt handling results
        """
        try:
            # Check if interrupts are enabled
            if not self.interrupt_config.global_settings.enabled:
                self.logger.info("Interrupts disabled, playing TTS normally")
                return tts_result

            # Extract audio path from TTS result
            audio_path = tts_result.outputs.get("audio_path")
            if not audio_path:
                self.logger.warning("No audio path in TTS result, cannot handle interrupts")
                return tts_result

            self.logger.info(
                "Starting TTS with REAL concurrent interrupt monitoring",
                action="handle_tts_with_real_concurrent_monitoring",
                input_data={"audio_path": audio_path},
                layer="interrupt_integration"
            )

            # Use the real concurrent monitoring method
            interrupt_result = await self.interrupt_handler.handle_tts_with_real_concurrent_monitoring(
                audio_path=audio_path,
                workflow_context=workflow_context
            )



            return interrupt_result

        except Exception as e:
            self.logger.error(
                "Error in real concurrent TTS interrupt handling",
                action="handle_tts_with_real_concurrent_monitoring",
                reason=str(e),
                layer="interrupt_integration"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Real concurrent interrupt handling error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def trigger_interrupt(self, user_input: str) -> StateOutput:
        """
        Trigger an interrupt with user input.
        
        This method can be called when an interrupt is detected externally
        (e.g., from voice activity detection, button press, etc.)
        
        Args:
            user_input: The user's interrupt input text
            
        Returns:
            StateOutput indicating interrupt was triggered
        """
        try:
            self.logger.info(
                "Triggering interrupt",
                action="trigger_interrupt",
                input_data={"user_input": user_input},
                layer="interrupt_integration"
            )
            
            # Store interrupt context in memory for the handler to pick up
            await self.memory_manager.set_interrupt_context(
                detected=True,
                confirmed=True,
                user_input_queued=user_input,
                handled=False
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt triggered successfully",
                code=StatusCode.OK,
                outputs={"interrupt_triggered": True, "user_input": user_input},
                meta={"interrupt_action": "triggered"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error triggering interrupt",
                action="trigger_interrupt",
                reason=str(e),
                layer="interrupt_integration"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Error triggering interrupt: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )





    async def start_early_interrupt_monitoring(self, workflow_context: Dict[str, Any] = None) -> StateOutput:
        """
        Start early interrupt monitoring during TTS generation phase.

        This allows users to interrupt even before TTS audio is generated,
        improving responsiveness by 2-5 seconds.

        Args:
            workflow_context: Current workflow context

        Returns:
            StateOutput indicating monitoring started
        """
        try:
            # Check if interrupts are enabled
            if not self.interrupt_config.global_settings.enabled:
                self.logger.info("Interrupts disabled, skipping early monitoring")
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="Interrupts disabled",
                    code=StatusCode.OK,
                    outputs={"early_monitoring_started": False},
                    meta={"interrupt_action": "disabled"}
                )

            self.logger.info(
                "Starting early interrupt monitoring during TTS generation",
                action="start_early_interrupt_monitoring",
                layer="interrupt_integration"
            )

            # Start background monitoring task
            await self.interrupt_handler.start_early_monitoring_task(workflow_context)

            return StateOutput(
                status=StatusType.SUCCESS,
                message="Early interrupt monitoring started",
                code=StatusCode.OK,
                outputs={"early_monitoring_started": True},
                meta={"interrupt_action": "early_monitoring_started"}
            )

        except Exception as e:
            self.logger.error(
                "Error starting early interrupt monitoring",
                action="start_early_interrupt_monitoring",
                reason=str(e),
                layer="interrupt_integration"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Early monitoring error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={"early_monitoring_started": False},
                meta={"error": str(e)}
            )

    async def clear_interrupt_state(self):
        """Clear all interrupt-related state."""
        try:
            await self.memory_manager.clear_interrupt_context()
            await self.memory_manager.set("contextual", "has_queued_input", False)
            await self.memory_manager.set("contextual", "queued_user_input", None)

            self.logger.info("Interrupt state cleared")

        except Exception as e:
            self.logger.error(f"Error clearing interrupt state: {e}")

    def is_interrupt_enabled(self) -> bool:
        """Check if interrupt handling is enabled."""
        return self.interrupt_config.global_settings.enabled
